USE satellite_system;

-- 删除已存在的表（如果有的话）
DROP TABLE IF EXISTS `operation_log`;

-- 创建操作日志表
CREATE TABLE `operation_log` (
  `id`            BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `request_time`  DATETIME      NOT NULL COMMENT '请求时间',
  `operator`      VARCHAR(16)   NOT NULL COMMENT '操作人',
  `title`         VARCHAR(32)   NOT NULL COMMENT '操作标题',
  `request_url`   VARCHAR(255)  NOT NULL COMMENT '请求地址',
  `request_method` VARCHAR(10)  NOT NULL COMMENT '请求方式 GET/POST...',
  `ip_address`    VARBINARY(16) NOT NULL COMMENT 'IP地址',
  `client`        VARCHAR(16)   NOT NULL COMMENT '客户端',
  `user_agent`    VARCHAR(512)  DEFAULT NULL COMMENT '浏览器 UA',
  `duration_ms`   INT           NOT NULL COMMENT '耗时(毫秒)',
  `request_param` JSO<PERSON>          DEFAULT NULL COMMENT '请求参数 JSON',
  `create_time`   DATETIME      DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';

-- 插入一些测试数据
INSERT INTO `operation_log` (
  `request_time`, `operator`, `title`, `request_url`, `request_method`,
  `ip_address`, `client`, `user_agent`, `duration_ms`, `request_param`
) VALUES
(
  '2025-07-29 16:00:00', '张三', '新建航天器', '/satellite/new', 'POST',
  INET_ATON('*************'), 'Web',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  150, '{"model":"CZ-5B","name":"长征五号B遥三"}'
),
(
  '2025-07-29 16:05:00', '李四', '删除航天器', '/satellite/delete', 'DELETE',
  INET_ATON('*************'), 'Web',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  89, '{"id":"1"}'
),
(
  '2025-07-29 16:10:00', '王五', '搜索操作日志', '/log/search', 'GET',
  INET_ATON('*************'), 'Web',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  45, NULL
);
