package com.xtgl.ssystem.util;

import org.junit.jupiter.api.Test;
import java.net.InetAddress;

/**
 * IP地址工具类测试
 */
public class IpUtilTest {

    @Test
    public void testIpv4Conversion() {
        String ipv4 = "***********";
        System.out.println("IPv4测试: " + ipv4);
        
        try {
            byte[] bytes = IpUtil.ipToBytes(ipv4);
            System.out.println("字节数组长度: " + bytes.length);
            System.out.println("字节数组内容: " + java.util.Arrays.toString(bytes));
            
            String converted = IpUtil.bytesToIp(bytes);
            System.out.println("转换回的IP: " + converted);
            System.out.println("是否相等: " + ipv4.equals(converted));
        } catch (Exception e) {
            System.err.println("IPv4转换失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("---");
    }

    @Test
    public void testIpv6Conversion() {
        String ipv6 = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
        System.out.println("IPv6测试: " + ipv6);
        
        try {
            byte[] bytes = IpUtil.ipToBytes(ipv6);
            System.out.println("字节数组长度: " + bytes.length);
            System.out.println("字节数组内容: " + java.util.Arrays.toString(bytes));
            
            String converted = IpUtil.bytesToIp(bytes);
            System.out.println("转换回的IP: " + converted);
        } catch (Exception e) {
            System.err.println("IPv6转换失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("---");
    }

    @Test
    public void testIpv6ShortForm() {
        String ipv6Short = "::1";
        System.out.println("IPv6简写测试: " + ipv6Short);
        
        try {
            byte[] bytes = IpUtil.ipToBytes(ipv6Short);
            System.out.println("字节数组长度: " + bytes.length);
            System.out.println("字节数组内容: " + java.util.Arrays.toString(bytes));
            
            String converted = IpUtil.bytesToIp(bytes);
            System.out.println("转换回的IP: " + converted);
        } catch (Exception e) {
            System.err.println("IPv6简写转换失败: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("---");
    }

    @Test
    public void testDirectInetAddress() {
        try {
            System.out.println("直接测试InetAddress:");
            
            // IPv4
            InetAddress ipv4 = InetAddress.getByName("***********");
            System.out.println("IPv4地址: " + ipv4.getHostAddress());
            System.out.println("IPv4字节长度: " + ipv4.getAddress().length);
            
            // IPv6
            InetAddress ipv6 = InetAddress.getByName("2001:0db8:85a3:0000:0000:8a2e:0370:7334");
            System.out.println("IPv6地址: " + ipv6.getHostAddress());
            System.out.println("IPv6字节长度: " + ipv6.getAddress().length);
            
            // IPv6简写
            InetAddress ipv6Short = InetAddress.getByName("::1");
            System.out.println("IPv6简写地址: " + ipv6Short.getHostAddress());
            System.out.println("IPv6简写字节长度: " + ipv6Short.getAddress().length);
            
        } catch (Exception e) {
            System.err.println("直接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
