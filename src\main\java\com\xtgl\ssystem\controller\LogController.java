package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.annotation.WebLog;
import com.xtgl.ssystem.common.dto.OptLogDTO;
import com.xtgl.ssystem.common.dto.OperationLogSearchDTO;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.Result;
import com.xtgl.ssystem.service.OperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 操作日志控制器
 */
@Slf4j
@RestController
@RequestMapping("/log")
public class LogController {

    @Autowired
    private OperationLogService operationLogService;

    /**
     * 记录用户在平台的操作日志
     *
     * @param logDto 操作日志DTO
     * @return 响应结果
     */
    @PostMapping
    @WebLog(title = "记录操作日志")
    public Result<?> recordLog(@RequestBody @Validated OptLogDTO logDto) {
        log.info("记录操作日志请求: {}", logDto.getTitle());
        try {
            operationLogService.saveOperationLog(logDto);
            return Result.success("操作日志记录成功");
        } catch (Exception e) {
            log.error("记录操作日志失败", e);
            return Result.error(HttpStatus.BAD_REQUEST.value(), "记录操作日志失败：" + e.getMessage());
        }
    }

    /**
     * 搜索操作日志
     * 支持根据操作人姓名搜索和时间区间筛选
     *
     * @param userName  操作人姓名（可选）
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @param pageNo    页码，默认1
     * @param pageSize  每页条数，默认10
     * @return 分页结果
     */
    @GetMapping("/search")
    @WebLog(title = "搜索操作日志", recordParam = false)
    public Result<PageResult<OptLogDTO>> searchLogs(
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        log.info("搜索操作日志请求 - 用户名: {}, 开始时间: {}, 结束时间: {}, 页码: {}, 每页条数: {}", 
                userName, startTime, endTime, pageNo, pageSize);
        
        try {
            OperationLogSearchDTO searchDto = new OperationLogSearchDTO();
            searchDto.setUserName(userName);
            searchDto.setStartTime(startTime);
            searchDto.setEndTime(endTime);
            searchDto.setPageNo(pageNo);
            searchDto.setPageSize(pageSize);
            
            PageResult<OptLogDTO> result = operationLogService.searchOperationLogs(searchDto);
            log.info("搜索到{}条操作日志", result.getTotal());
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("搜索操作日志失败", e);
            return Result.error("搜索操作日志失败：" + e.getMessage());
        }
    }
}
