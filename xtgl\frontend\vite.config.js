import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    port: 5175,
    proxy: {
      '/mqtt': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/satellite': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      },
      '/single': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      },
      '/algorithm': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      },
      '/log': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  }
}) 