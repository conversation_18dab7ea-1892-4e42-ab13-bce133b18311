package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志实体类
 */
@Data
@TableName("operation_log")
public class OperationLog {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 请求时间
     */
    @TableField("request_time")
    private LocalDateTime requestTime;

    /**
     * 操作人
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作标题
     */
    @TableField("title")
    private String title;

    /**
     * 请求地址
     */
    @TableField("request_url")
    private String requestUrl;

    /**
     * 请求方式 GET/POST...
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private byte[] ipAddress;

    /**
     * 客户端
     */
    @TableField("client")
    private String client;

    /**
     * 浏览器 UA
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 耗时(毫秒)
     */
    @TableField("duration_ms")
    private Integer durationMs;

    /**
     * 请求参数 JSON
     */
    @TableField("request_param")
    private String requestParam;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
