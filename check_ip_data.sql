-- 检查operation_log表中的IP地址数据
USE satellite_system;

-- 查看operation_log表中IP地址的长度分布
SELECT 
    LENGTH(ip_address) as ip_length,
    COUNT(*) as count,
    HEX(ip_address) as hex_sample
FROM operation_log 
GROUP BY LENGTH(ip_address)
ORDER BY ip_length;

-- 查看最近10条记录的IP地址信息
SELECT 
    id,
    operator,
    title,
    LENGTH(ip_address) as ip_length,
    HEX(ip_address) as ip_hex,
    create_time
FROM operation_log 
ORDER BY create_time DESC 
LIMIT 10;

-- 查看mqtt_table表中IP地址的长度分布（对比）
SELECT 
    LENGTH(ip) as ip_length,
    COUNT(*) as count,
    HEX(ip) as hex_sample
FROM mqtt_table 
GROUP BY LENGTH(ip)
ORDER BY ip_length;

-- 查看mqtt_table中的IP地址示例
SELECT 
    id,
    name,
    LENGTH(ip) as ip_length,
    HEX(ip) as ip_hex,
    INET_NTOA(CONV(HEX(ip), 16, 10)) as ip_string
FROM mqtt_table 
LIMIT 5;
