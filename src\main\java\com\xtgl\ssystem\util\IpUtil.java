package com.xtgl.ssystem.util;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址转换工具类
 */
public class IpUtil {

    /**
     * 将IP字符串转换为字节数组
     *
     * @param ipString IP字符串，如"***********"
     * @return 字节数组
     */
    public static byte[] ipToBytes(String ipString) {
        try {
            return InetAddress.getByName(ipString).getAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException("IP地址格式错误: " + ipString, e);
        }
    }

    /**
     * 将字节数组转换为IP字符串
     *
     * @param ipBytes 字节数组
     * @return IP字符串，如"***********"
     */
    public static String bytesToIp(byte[] ipBytes) {
        if (ipBytes == null) {
            return "未知";
        }

        // 处理IPv4地址（4字节）
        if (ipBytes.length == 4) {
            try {
                return InetAddress.getByAddress(ipBytes).getHostAddress();
            } catch (UnknownHostException e) {
                return "格式错误";
            }
        }

        // 处理IPv6地址（16字节）
        if (ipBytes.length == 16) {
            try {
                return InetAddress.getByAddress(ipBytes).getHostAddress();
            } catch (UnknownHostException e) {
                return "格式错误";
            }
        }

        // 检查是否是UUID格式的错误数据（通常是36字节）
        if (ipBytes.length == 36) {
            try {
                String possibleUuid = new String(ipBytes, "UTF-8");
                if (possibleUuid.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")) {
                    return "错误格式(UUID)";
                }
            } catch (Exception e) {
                // 忽略转换错误
            }
        }

        // 尝试检查是否是字符串格式的IP地址被错误存储
        if (ipBytes.length > 7 && ipBytes.length < 40) {
            try {
                String possibleIp = new String(ipBytes, "UTF-8");
                // 检查是否是有效的IP地址格式
                if (possibleIp.matches("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$") ||
                    possibleIp.matches("^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$")) {
                    return possibleIp + "(修复)";
                }
            } catch (Exception e) {
                // 忽略转换错误
            }
        }

        // 处理其他长度的字节数组，尝试转换为可读格式
        if (ipBytes.length > 0 && ipBytes.length <= 16) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < ipBytes.length; i++) {
                if (i > 0) sb.append(".");
                sb.append(ipBytes[i] & 0xFF);
            }
            return sb.toString();
        }

        return String.format("未知格式(%d字节)", ipBytes.length);
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个非unknown的有效IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }
}