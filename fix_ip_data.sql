-- 修复operation_log表中错误的IP地址数据
USE satellite_system;

-- 1. 查看当前IP地址数据的问题
SELECT 
    id,
    operator,
    title,
    LENGTH(ip_address) as ip_length,
    HEX(ip_address) as ip_hex,
    create_time
FROM operation_log 
WHERE LENGTH(ip_address) != 4 AND LENGTH(ip_address) != 16
ORDER BY create_time DESC 
LIMIT 10;

-- 2. 备份原始数据（可选）
CREATE TABLE IF NOT EXISTS operation_log_backup AS 
SELECT * FROM operation_log WHERE LENGTH(ip_address) != 4 AND LENGTH(ip_address) != 16;

-- 3. 修复错误的IP地址数据
-- 将所有非标准长度的IP地址重置为127.0.0.1（IPv4格式）
UPDATE operation_log 
SET ip_address = INET_ATON('127.0.0.1')
WHERE LENGTH(ip_address) != 4 AND LENGTH(ip_address) != 16;

-- 4. 验证修复结果
SELECT 
    LENGTH(ip_address) as ip_length,
    COUNT(*) as count,
    INET_NTOA(CONV(HEX(ip_address), 16, 10)) as sample_ip
FROM operation_log 
GROUP BY LENGTH(ip_address)
ORDER BY ip_length;

-- 5. 查看修复后的最新记录
SELECT 
    id,
    operator,
    title,
    INET_NTOA(CONV(HEX(ip_address), 16, 10)) as ip_address,
    create_time
FROM operation_log 
ORDER BY create_time DESC 
LIMIT 10;
