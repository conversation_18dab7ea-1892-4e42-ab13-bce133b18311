<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.OperationLogMapper">

    <!-- 分页搜索操作日志 -->
    <select id="searchOperationLogs" resultType="com.xtgl.ssystem.common.entity.OperationLog">
        SELECT * FROM operation_log
        <where>
            <if test="userName != null and userName != ''">
                AND operator LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
